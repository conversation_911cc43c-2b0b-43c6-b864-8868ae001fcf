/**
 * CythroDash - Security Logs Test API Route
 * 
 * This endpoint is for testing security log creation.
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { SecurityLogsController } from '@/hooks/managers/controller/Security/Logs';
import { SecurityLogAction, SecurityLogSeverity } from '@/database/tables/cythro_dash_users_logs';

export async function POST(request: NextRequest) {
  try {
    console.log('Security logs test API called');

    // Parse request body
    const body = await request.json();
    const { user_id, action } = body;

    if (!user_id) {
      return NextResponse.json(
        {
          success: false,
          message: 'user_id is required'
        },
        { status: 400 }
      );
    }

    // Get IP address and user agent from request
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     request.ip || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    console.log('Creating test security log for user:', user_id);

    // Create a test security log
    const result = await SecurityLogsController.createLog({
      user_id: user_id,
      action: action || SecurityLogAction.LOGIN_SUCCESS,
      severity: SecurityLogSeverity.LOW,
      description: 'Test security log entry',
      details: {
        test: true,
        timestamp: new Date().toISOString(),
        api_endpoint: '/api/test/security-logs'
      },
      ip_address: ipAddress,
      user_agent: userAgent,
      is_suspicious: false,
      requires_attention: false
    });

    console.log('Test security log creation result:', result);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Test security log created successfully',
        log: result.log
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          message: result.message,
          errors: result.errors
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Security logs test API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('Getting security logs test info');

    return NextResponse.json({
      success: true,
      message: 'Security logs test endpoint',
      usage: {
        method: 'POST',
        body: {
          user_id: 'number (required)',
          action: 'string (optional, defaults to login_success)'
        },
        example: {
          user_id: 17,
          action: 'login_success'
        }
      }
    });

  } catch (error) {
    console.error('Security logs test GET error:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
